#!/usr/bin/env python3
"""
Integration tests for Composio MCP integration

These tests validate the complete flow:
1. Endpoint querying
2. MCP URL generation via Composio API
3. Database storage in Supabase
4. MCP server communication
5. Auth URL retrieval

Run with: python -m pytest tests/test_composio_mcp_integration.py -v
"""

import pytest
import asyncio
import httpx
import json
import os
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import TestClient
from services.composio_integration import composio_mcp_service, ComposioMCPConnection
from api.composio_mcp import router
from fastapi import FastAPI

# Create test app
test_app = FastAPI()
test_app.include_router(router)

class TestComposioMCPIntegration:
    """Test suite for Composio MCP integration"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(test_app)
    
    @pytest.fixture
    def mock_user_id(self):
        """Mock user ID for testing"""
        return "test_user_12345"
    
    @pytest.fixture
    def mock_app_key(self):
        """Mock app key for testing"""
        return "gmail"
    
    @pytest.fixture
    def mock_composio_response(self):
        """Mock Composio API response"""
        return {
            "sseUrl": "https://mcp.composio.dev/partner/composio/gmail/sse?customerId=test-session-uuid",
            "status": "success"
        }
    
    @pytest.fixture
    def mock_mcp_url(self):
        """Mock MCP URL"""
        return "https://mcp.composio.dev/partner/composio/gmail/sse?customerId=test-session-uuid"

class TestComposioService:
    """Test the Composio MCP service directly"""
    
    @pytest.mark.asyncio
    async def test_generate_session_uuid(self):
        """Test session UUID generation"""
        user_id = "test_user"
        app_key = "gmail"
        
        uuid1 = composio_mcp_service._generate_session_uuid(user_id, app_key)
        uuid2 = composio_mcp_service._generate_session_uuid(user_id, app_key)
        
        # UUIDs should be different each time (includes random component)
        assert uuid1 != uuid2
        assert len(uuid1) == 36  # Standard UUID length
        assert len(uuid2) == 36
    
    @pytest.mark.asyncio
    async def test_extract_mcp_url(self):
        """Test MCP URL extraction from various response formats"""
        service = composio_mcp_service
        app_key = "gmail"
        session_uuid = "test-session"
        
        # Test direct sseUrl
        response1 = {"sseUrl": "https://mcp.composio.dev/test/url"}
        url1 = service._extract_mcp_url(response1, app_key, session_uuid)
        assert url1 == "https://mcp.composio.dev/test/url"
        
        # Test nested data.url
        response2 = {"data": {"url": "https://mcp.composio.dev/nested/url"}}
        url2 = service._extract_mcp_url(response2, app_key, session_uuid)
        assert url2 == "https://mcp.composio.dev/nested/url"
        
        # Test fallback pattern construction
        response3 = {"status": "success"}  # No URL in response
        url3 = service._extract_mcp_url(response3, app_key, session_uuid)
        expected_pattern = f"https://mcp.composio.dev/partner/composio/{app_key}/mcp?customerId={session_uuid}"
        assert url3 == expected_pattern
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_generate_composio_mcp_url_success(self, mock_client):
        """Test successful MCP URL generation from Composio"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "sseUrl": "https://mcp.composio.dev/partner/composio/gmail/sse?customerId=test-uuid"
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        app_key = "gmail"
        session_uuid = "test-uuid"
        
        url = await composio_mcp_service._generate_composio_mcp_url(app_key, session_uuid)
        
        assert url == "https://mcp.composio.dev/partner/composio/gmail/sse?customerId=test-uuid"
        mock_client_instance.get.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    async def test_generate_composio_mcp_url_fallback_to_install(self, mock_client):
        """Test fallback to installation endpoint when status check fails"""
        # Mock status check failure, installation success
        mock_status_response = MagicMock()
        mock_status_response.status_code = 404
        
        mock_install_response = MagicMock()
        mock_install_response.status_code = 200
        mock_install_response.json.return_value = {
            "data": {"url": "https://mcp.composio.dev/install/success"}
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_status_response
        mock_client_instance.post.return_value = mock_install_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        app_key = "slack"
        session_uuid = "test-uuid-2"
        
        url = await composio_mcp_service._generate_composio_mcp_url(app_key, session_uuid)
        
        assert url == "https://mcp.composio.dev/install/success"
        mock_client_instance.get.assert_called_once()
        mock_client_instance.post.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('services.composio_integration.create_client')
    async def test_store_mcp_connection(self, mock_create_client):
        """Test storing MCP connection in Supabase"""
        # Mock Supabase client
        mock_supabase = MagicMock()
        mock_table = MagicMock()
        mock_upsert = MagicMock()
        mock_execute = MagicMock()
        
        mock_execute.execute.return_value = MagicMock(data=[{"id": "test"}])
        mock_upsert.execute.return_value = mock_execute
        mock_table.upsert.return_value = mock_upsert
        mock_supabase.table.return_value = mock_table
        mock_create_client.return_value = mock_supabase
        
        # Override the service's supabase client
        composio_mcp_service.supabase = mock_supabase
        
        user_id = "test_user"
        app_key = "github"
        mcp_url = "https://mcp.composio.dev/test"
        session_uuid = "test-session"
        
        result = await composio_mcp_service._store_mcp_connection(
            user_id, app_key, mcp_url, session_uuid
        )
        
        assert result is True
        mock_supabase.table.assert_called_with("mcp_oauth_tokens")
        mock_table.upsert.assert_called_once()
        
        # Check the data structure
        call_args = mock_table.upsert.call_args[0][0]
        assert call_args["user_id"] == user_id
        assert call_args["qualified_name"] == f"composio/{app_key}"
        assert call_args["access_token"] == mcp_url
        assert call_args["refresh_token"] == session_uuid

class TestComposioAPI:
    """Test the FastAPI endpoints"""
    
    @pytest.mark.asyncio
    @patch('api.composio_mcp.get_current_user_id_from_jwt')
    @patch('services.composio_integration.composio_mcp_service.create_user_mcp_connection')
    async def test_create_connection_endpoint_success(self, mock_create_connection, mock_get_user):
        """Test successful connection creation via API endpoint"""
        # Mock dependencies
        mock_get_user.return_value = "test_user_123"
        mock_create_connection.return_value = ComposioMCPConnection(
            success=True,
            app_key="gmail",
            mcp_url="https://mcp.composio.dev/test",
            session_uuid="test-session",
            auth_url="https://mcp.composio.dev/test",
            qualified_name="composio/gmail"
        )
        
        client = TestClient(test_app)
        
        response = client.post(
            "/composio-mcp/create-connection",
            json={"app_key": "gmail"},
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["app_key"] == "gmail"
        assert data["auth_url"] == "https://mcp.composio.dev/test"
        assert data["qualified_name"] == "composio/gmail"
    
    @pytest.mark.asyncio
    @patch('api.composio_mcp.get_current_user_id_from_jwt')
    @patch('services.composio_integration.composio_mcp_service.create_user_mcp_connection')
    async def test_create_connection_endpoint_failure(self, mock_create_connection, mock_get_user):
        """Test connection creation failure via API endpoint"""
        # Mock dependencies
        mock_get_user.return_value = "test_user_123"
        mock_create_connection.return_value = ComposioMCPConnection(
            success=False,
            app_key="invalid_app",
            error="Composio API returned error"
        )
        
        client = TestClient(test_app)
        
        response = client.post(
            "/composio-mcp/create-connection",
            json={"app_key": "invalid_app"},
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200  # API returns 200 but success=False
        data = response.json()
        assert data["success"] is False
        assert data["error"] == "Composio API returned error"
    
    def test_get_supported_apps_endpoint(self):
        """Test the supported apps endpoint"""
        client = TestClient(test_app)
        
        response = client.get("/composio-mcp/supported-apps")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "apps" in data
        assert len(data["apps"]) > 0
        
        # Check that Gmail is in the supported apps
        app_keys = [app["key"] for app in data["apps"]]
        assert "gmail" in app_keys
    
    def test_health_endpoint(self):
        """Test the health check endpoint"""
        client = TestClient(test_app)
        
        response = client.get("/composio-mcp/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "composio_mcp_integration"

class TestEndToEndFlow:
    """Test the complete end-to-end flow"""
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient')
    @patch('services.composio_integration.create_client')
    @patch('mcp.client.sse.sse_client')
    async def test_complete_flow_success(self, mock_sse_client, mock_create_client, mock_http_client):
        """Test the complete flow from API call to auth URL"""
        # Mock Composio API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "sseUrl": "https://mcp.composio.dev/partner/composio/gmail/sse?customerId=test-uuid"
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_response
        mock_http_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Mock Supabase storage
        mock_supabase = MagicMock()
        mock_table = MagicMock()
        mock_upsert = MagicMock()
        mock_execute = MagicMock()
        
        mock_execute.execute.return_value = MagicMock(data=[{"id": "test"}])
        mock_upsert.execute.return_value = mock_execute
        mock_table.upsert.return_value = mock_upsert
        mock_supabase.table.return_value = mock_table
        mock_create_client.return_value = mock_supabase
        
        # Mock MCP connection
        mock_session = AsyncMock()
        mock_session.initialize = AsyncMock()
        mock_session.list_tools = AsyncMock()
        mock_session.list_tools.return_value = MagicMock(tools=[])
        
        mock_sse_context = AsyncMock()
        mock_sse_context.__aenter__.return_value = (MagicMock(), MagicMock())
        mock_sse_client.return_value = mock_sse_context
        
        # Test the complete flow
        user_id = "test_user"
        app_key = "gmail"
        
        result = await composio_mcp_service.create_user_mcp_connection(user_id, app_key)
        
        assert result.success is True
        assert result.app_key == app_key
        assert result.mcp_url == "https://mcp.composio.dev/partner/composio/gmail/sse?customerId=test-uuid"
        assert result.auth_url is not None
        assert result.qualified_name == "composio/gmail"

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
